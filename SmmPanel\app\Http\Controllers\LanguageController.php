<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    /**
     * Chuyển đổi ngôn ngữ
     */
    public function switchLanguage(Request $request, $locale)
    {
        // Danh sách ngôn ngữ được hỗ trợ
        $supportedLocales = ['vi', 'en'];
        
        // Kiểm tra ngôn ngữ có được hỗ trợ không
        if (in_array($locale, $supportedLocales)) {
            Session::put('locale', $locale);
        }
        
        // Quay lại trang trước đó
        return redirect()->back();
    }
}
