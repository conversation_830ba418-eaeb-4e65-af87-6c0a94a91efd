<?php $__env->startSection('title', __('Register') . ' - ' . __('SMM Panel')); ?>

<?php $__env->startSection('content'); ?>
<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <div class="auth-card">
                    <div class="auth-header">
                        <div class="mb-3">
                            <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                <i class="fas fa-user-plus" style="font-size: 24px; color: white;"></i>
                            </div>
                        </div>
                        <h3 class="mb-2"><?php echo e(__('Create Account')); ?></h3>
                        <p class="mb-0"><?php echo e(__('Join us today and get started')); ?></p>
                    </div>
                    
                    <div class="auth-body">
                        <form method="POST" action="<?php echo e(route('register')); ?>">
                            <?php echo csrf_field(); ?>
                            
                            <div class="mb-4">
                                <label for="name" class="form-label">
                                    <?php echo e(__('Full Name')); ?>

                                </label>
                                <div class="position-relative">
                                    <input type="text"
                                           class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="name"
                                           name="name"
                                           value="<?php echo e(old('name')); ?>"
                                           placeholder="<?php echo e(__('Enter your full name')); ?>"
                                           style="padding-left: 50px;"
                                           required>
                                    <i class="fas fa-user position-absolute" style="left: 18px; top: 50%; transform: translateY(-50%); color: #9ca3af;"></i>
                                </div>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-4">
                                <label for="email" class="form-label">
                                    <?php echo e(__('Email Address')); ?>

                                </label>
                                <div class="position-relative">
                                    <input type="email"
                                           class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="email"
                                           name="email"
                                           value="<?php echo e(old('email')); ?>"
                                           placeholder="<?php echo e(__('Enter your email')); ?>"
                                           style="padding-left: 50px;"
                                           required>
                                    <i class="fas fa-envelope position-absolute" style="left: 18px; top: 50%; transform: translateY(-50%); color: #9ca3af;"></i>
                                </div>
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <?php echo e(__('Password')); ?>

                                </label>
                                <div class="position-relative">
                                    <input type="password"
                                           class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="password"
                                           name="password"
                                           placeholder="<?php echo e(__('Enter your password')); ?>"
                                           style="padding-left: 50px; padding-right: 50px;"
                                           required>
                                    <i class="fas fa-lock position-absolute" style="left: 18px; top: 50%; transform: translateY(-50%); color: #9ca3af;"></i>
                                    <button type="button"
                                            class="btn position-absolute end-0 top-50 translate-middle-y pe-3"
                                            onclick="togglePassword('password')"
                                            style="border: none; background: none; color: #9ca3af; z-index: 10;">
                                        <i class="fas fa-eye" id="toggleIcon1"></i>
                                    </button>
                                </div>
                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-4">
                                <label for="password_confirmation" class="form-label">
                                    <?php echo e(__('Confirm Password')); ?>

                                </label>
                                <div class="position-relative">
                                    <input type="password"
                                           class="form-control"
                                           id="password_confirmation"
                                           name="password_confirmation"
                                           placeholder="<?php echo e(__('Confirm your password')); ?>"
                                           style="padding-left: 50px; padding-right: 50px;"
                                           required>
                                    <i class="fas fa-lock position-absolute" style="left: 18px; top: 50%; transform: translateY(-50%); color: #9ca3af;"></i>
                                    <button type="button"
                                            class="btn position-absolute end-0 top-50 translate-middle-y pe-3"
                                            onclick="togglePassword('password_confirmation')"
                                            style="border: none; background: none; color: #9ca3af; z-index: 10;">
                                        <i class="fas fa-eye" id="toggleIcon2"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-success">
                                    <?php echo e(__('Create Account')); ?>

                                </button>
                            </div>

                            <div class="text-center">
                                <p class="mb-0" style="color: #6b7280; font-weight: 500;">
                                    <?php echo e(__('Already have an account?')); ?>

                                    <a href="<?php echo e(route('login')); ?>" class="text-decoration-none" style="color: #2a5298; font-weight: 700;">
                                        <?php echo e(__('Sign In')); ?>

                                    </a>
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const toggleIcon = document.getElementById(fieldId === 'password' ? 'toggleIcon1' : 'toggleIcon2');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Src Làm\SmmPanel\resources\views/auth/register.blade.php ENDPATH**/ ?>