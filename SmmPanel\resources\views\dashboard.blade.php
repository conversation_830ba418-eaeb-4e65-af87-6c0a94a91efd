@extends('layouts.app')

@section('title', __('Dashboard') . ' - ' . __('SMM Panel'))

@section('content')
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>{{ __('Dashboard') }}
                </h2>
                <div class="text-muted">
                    {{ __('Welcome') }}, {{ Auth::user()->name }}!
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="dashboard-card p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 p-3 rounded-circle">
                            <i class="fas fa-shopping-cart text-primary fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-0 text-muted">{{ __('Orders') }}</h6>
                        <h4 class="mb-0">0</h4>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="dashboard-card p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 p-3 rounded-circle">
                            <i class="fas fa-wallet text-success fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-0 text-muted">{{ __('Balance') }}</h6>
                        <h4 class="mb-0">$0.00</h4>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="dashboard-card p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 p-3 rounded-circle">
                            <i class="fas fa-cogs text-info fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-0 text-muted">{{ __('Services') }}</h6>
                        <h4 class="mb-0">0</h4>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="dashboard-card p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 p-3 rounded-circle">
                            <i class="fas fa-chart-line text-warning fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-0 text-muted">{{ __('Status') }}</h6>
                        <h4 class="mb-0">{{ __('Active') }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-card p-4">
                <h5 class="mb-3">
                    <i class="fas fa-bolt me-2"></i>{{ __('Quick Actions') }}
                </h5>
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus me-2"></i>{{ __('Add Funds') }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-shopping-cart me-2"></i>{{ __('New Order') }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-info w-100">
                            <i class="fas fa-history me-2"></i>{{ __('History') }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-headset me-2"></i>{{ __('Support') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="row">
        <div class="col-md-8">
            <div class="dashboard-card p-4">
                <h5 class="mb-3">
                    <i class="fas fa-clock me-2"></i>{{ __('Recent Activity') }}
                </h5>
                <div class="text-center py-5 text-muted">
                    <i class="fas fa-inbox fa-3x mb-3 opacity-50"></i>
                    <p class="mb-0">{{ __('No data available') }}</p>
                    <small>{{ __('Your recent activities will appear here') }}</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="dashboard-card p-4">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>{{ __('Account Info') }}
                </h5>
                <div class="mb-3">
                    <small class="text-muted">{{ __('Name') }}</small>
                    <div class="fw-bold">{{ Auth::user()->name }}</div>
                </div>
                <div class="mb-3">
                    <small class="text-muted">{{ __('Email') }}</small>
                    <div class="fw-bold">{{ Auth::user()->email }}</div>
                </div>
                <div class="mb-3">
                    <small class="text-muted">{{ __('Member Since') }}</small>
                    <div class="fw-bold">{{ Auth::user()->created_at->format('M d, Y') }}</div>
                </div>
                <hr>
                <div class="d-grid">
                    <a href="#" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-user-cog me-2"></i>{{ __('Edit Profile') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
.quick-action-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
</style>
@endsection
