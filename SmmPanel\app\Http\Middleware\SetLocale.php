<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Danh sách ngôn ngữ được hỗ trợ
        $supportedLocales = ['vi', 'en'];
        
        // Lấy ngôn ngữ từ session hoặc mặc định
        $locale = Session::get('locale', config('app.locale'));
        
        // Kiểm tra nếu có yêu cầu thay đổi ngôn ngữ
        if ($request->has('lang') && in_array($request->get('lang'), $supportedLocales)) {
            $locale = $request->get('lang');
            Session::put('locale', $locale);
        }
        
        // Đảm bảo ngôn ngữ được hỗ trợ
        if (!in_array($locale, $supportedLocales)) {
            $locale = config('app.fallback_locale');
        }
        
        // Thiết lập ngôn ngữ cho ứng dụng
        App::setLocale($locale);
        
        return $next($request);
    }
}
